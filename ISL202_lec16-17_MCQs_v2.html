<!DOCTYPE html>

<html lang="en">

<head>
  <meta charset="utf-8" />
  <meta content="width=device-width,initial-scale=1" name="viewport" />
  <title>ISL202_lec16_MCQs — Lecture 16 (pp.72-78)</title>
  <style>
    body {
      font-family: system-u<PERSON>, Segoe UI, Roboto, Arial;
      margin: 18px;
      background: #f7f7fb;
      color: #111;
    }

    .card {
      max-width: 900px;
      margin: 12px auto;
      padding: 18px;
      background: #fff;
      border-radius: 12px;
      box-shadow: 0 6px 24px rgba(20, 20, 40, 0.08);
    }

    h1 {
      margin: 0 0 8px;
      font-size: 20px;
    }

    .meta {
      color: #444;
      margin-bottom: 12px;
    }

    .qcount {
      float: right;
      color: #666;
      font-size: 13px;
    }

    .stem {
      font-size: 18px;
      margin: 12px 0;
    }

    .options {
      list-style: none;
      padding: 0;
      margin: 8px 0;
    }

    .options li {
      margin: 8px 0;
    }

    button.opt {
      width: 100%;
      text-align: left;
      padding: 10px;
      border-radius: 8px;
      border: 1px solid #e6e6ef;
      background: #fbfbff;
      cursor: pointer;
      font-size: 15px;
    }

    button.opt.correct {
      border-color: #2e7d32;
      background: #e8f5e9;
    }

    button.opt.incorrect {
      border-color: #c62828;
      background: #ffebee;
    }

    .nav {
      display: flex;
      gap: 8px;
      margin-top: 12px;
    }

    .stats {
      margin-top: 12px;
      padding: 10px;
      background: #f2f4ff;
      border-radius: 8px;
      font-size: 14px;
    }

    .small {
      font-size: 13px;
      color: #666;
    }

    .source {
      margin-top: 8px;
      font-size: 13px;
      color: #222;
      background: #f8f9ff;
      padding: 8px;
      border-radius: 6px;
    }

    .controls {
      display: flex;
      gap: 8px;
      flex-wrap: wrap;
      margin-top: 12px;
    }

    .btn {
      padding: 8px 12px;
      border-radius: 8px;
      border: 0;
      cursor: pointer;
    }

    .btn.primary {
      background: #2b6ef6;
      color: #fff;
    }

    .btn.warn {
      background: #ff7043;
      color: #fff;
    }

    .btn.ghost {
      background: #fff;
      border: 1px solid #ddd;
    }

    .locked {
      opacity: 0.6;
      pointer-events: none;
    }
  </style>
</head>

<body>
  <div class="card" role="main">
    <h1>ISL202 — Lecture 16–17 MCQs (pages 72–83)</h1>
    <div class="meta">
      One question at a time. Select an option to get immediate feedback.
      Progress stored in <code>localStorage</code> under key
      <strong>Combined Handouts ISL202.pdf_lec16-17</strong>.
    </div>
    <div id="quiz"></div>
    <div class="controls">
      <button class="btn ghost" id="prevBtn">Previous</button>
      <button class="btn ghost" id="nextBtn">Next</button>
      <button class="btn primary" id="submitBtn">
        Submit (lock answers)
      </button>
      <button class="btn warn" id="resetBtn">
        Reset stats &amp; answers
      </button>
      <div style="flex: 1"></div>
      <div class="small">
        File source: <strong>Combined Handouts ISL202.pdf</strong> — Lecture
        pages
        <strong>72–83</strong>
      </div>
    </div>
    <div class="stats" id="stats">
      <div>Total attempted: <span id="attempted">0</span></div>
      <div>
        Correct: <span id="correct">0</span> — Incorrect:
        <span id="incorrect">0</span>
      </div>
      <div>Accuracy: <span id="accuracy">0</span>%</div>
      <div class="small">
        Reloading the page preserves progress for this lecture.
      </div>
    </div>
    <div class="source small" id="sourceDisplay"></div>
  </div>
  <script>
    document.addEventListener("DOMContentLoaded", function () {
      // Load MCQs from external JSON file
      let QUESTIONS = [];
      fetch('ISL202_lec16-17_MCQs.json')
        .then(response => response.json())
        .then(data => {
          QUESTIONS = data;
          // initial render & stats
          updateStats();
          render();
        });

      const PDFNAME = "Combined Handouts ISL202.pdf";
      const LECT_ID = "lec16-17";
      const STORAGE_KEY = PDFNAME + "_" + LECT_ID;

      let state = {
        current: 0,
        answers: {}, // id -> selected index
        locked: false,
      };

      // restore from localStorage
      const saved = localStorage.getItem(STORAGE_KEY);
      if (saved) {
        try {
          const s = JSON.parse(saved);
          state = Object.assign(state, s);
        } catch (e) {
          console.warn(e);
        }
      }

      function saveState() {
        localStorage.setItem(STORAGE_KEY, JSON.stringify(state));
      }

      function updateStats() {
        let attempted = 0,
          correct = 0,
          incorrect = 0;
        for (const q of QUESTIONS) {
          const sel = state.answers[q.id];
          if (typeof sel === "number") {
            attempted++;
            if (sel === q.answerIndex) correct++;
            else incorrect++;
          }
        }
        document.getElementById("attempted").textContent = attempted;
        document.getElementById("correct").textContent = correct;
        document.getElementById("incorrect").textContent = incorrect;
        document.getElementById("accuracy").textContent = attempted
          ? Math.round((correct / attempted) * 100)
          : 0;
      }

      function render() {
        const q = QUESTIONS[state.current];
        const container = document.getElementById("quiz");
        container.innerHTML = `
      <div><span class="qcount">Question ${state.current + 1} / ${QUESTIONS.length
          }</span></div>
      <div class="stem">${q.text}</div>
      <ul class="options" id="opts"></ul>
      <div id="feedback" style="margin-top:8px"></div>
    `;
        const opts = document.getElementById("opts");
        q.options.forEach((text, i) => {
          const li = document.createElement("li");
          const btn = document.createElement("button");
          btn.className = "opt";
          btn.textContent = String.fromCharCode(65 + i) + ". " + text;
          btn.onclick = () => selectOption(i);
          // style if already answered
          const sel = state.answers[q.id];
          if (typeof sel === "number") {
            if (i === sel) {
              if (state.locked)
                btn.classList.add(
                  i === q.answerIndex ? "correct" : "incorrect"
                );
              else
                btn.classList.add(
                  i === q.answerIndex ? "correct" : "incorrect"
                );
            }
          }
          li.appendChild(btn);
          opts.appendChild(li);
        });
        document.getElementById("sourceDisplay").textContent =
          "Source page(s): " + q.sourcePages;
        // lock UI if submitted
        document
          .getElementById("submitBtn")
          .classList.toggle("locked", state.locked);
        document.getElementById("prevBtn").disabled = state.current === 0;
        document.getElementById("nextBtn").disabled =
          state.current === QUESTIONS.length - 1;
        // Dynamic heading update
        const currentLecture = QUESTIONS[state.current].lecture;
        const currentPages = QUESTIONS[state.current].sourcePages;
        document.querySelector(
          "h1"
        ).textContent = `ISL202 — ${currentLecture} MCQs (pages ${currentPages})`;
      }

      function selectOption(idx) {
        if (state.locked) {
          alert("Answers are locked for this session. Reset to reattempt.");
          return;
        }
        const q = QUESTIONS[state.current];
        const prev = state.answers[q.id];
        // update answer
        state.answers[q.id] = idx;
        // immediate feedback
        const opts = document.querySelectorAll(".opt");
        opts.forEach((b, i) => {
          b.classList.remove("correct", "incorrect");
          if (i === idx) {
            if (idx === q.answerIndex) b.classList.add("correct");
            else b.classList.add("incorrect");
          }
        });
        // update stats: if previously incorrect and now correct, adjust counts via state.answers
        updateStats();
        saveState();
      }

      document.getElementById("prevBtn").addEventListener("click", () => {
        state.current = Math.max(0, state.current - 1);
        render();
      });
      document.getElementById("nextBtn").addEventListener("click", () => {
        state.current = Math.min(QUESTIONS.length - 1, state.current + 1);
        render();
      });
      document.getElementById("resetBtn").addEventListener("click", () => {
        if (!confirm("Reset this lecture's stats and saved answers?")) return;
        state.answers = {};
        state.current = 0;
        state.locked = false;
        saveState();
        render();
      });
      document.getElementById("submitBtn").addEventListener("click", () => {
        if (
          !confirm(
            "Submit and lock answers for this session? You can still Reset afterwards."
          )
        )
          return;
        state.locked = true;
        saveState();
        render();
      });

      // (initial render & stats now only called after questions are loaded)
    });
  </script>
</body>

</html>