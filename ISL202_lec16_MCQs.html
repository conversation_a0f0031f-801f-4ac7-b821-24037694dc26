<!doctype html>
<html lang="en">

<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width,initial-scale=1" />
  <title>ISL202_lec16_MCQs — Lecture 16 (pp.72-78)</title>
  <style>
    body {
      font-family: system-ui, Segoe UI, Roboto, Arial;
      margin: 18px;
      background: #f7f7fb;
      color: #111
    }

    .card {
      max-width: 900px;
      margin: 12px auto;
      padding: 18px;
      background: #fff;
      border-radius: 12px;
      box-shadow: 0 6px 24px rgba(20, 20, 40, .08)
    }

    h1 {
      margin: 0 0 8px;
      font-size: 20px
    }

    .meta {
      color: #444;
      margin-bottom: 12px
    }

    .qcount {
      float: right;
      color: #666;
      font-size: 13px
    }

    .stem {
      font-size: 18px;
      margin: 12px 0
    }

    .options {
      list-style: none;
      padding: 0;
      margin: 8px 0
    }

    .options li {
      margin: 8px 0
    }

    button.opt {
      width: 100%;
      text-align: left;
      padding: 10px;
      border-radius: 8px;
      border: 1px solid #e6e6ef;
      background: #fbfbff;
      cursor: pointer;
      font-size: 15px
    }

    button.opt.correct {
      border-color: #2e7d32;
      background: #e8f5e9
    }

    button.opt.incorrect {
      border-color: #c62828;
      background: #ffebee
    }

    .nav {
      display: flex;
      gap: 8px;
      margin-top: 12px
    }

    .stats {
      margin-top: 12px;
      padding: 10px;
      background: #f2f4ff;
      border-radius: 8px;
      font-size: 14px
    }

    .small {
      font-size: 13px;
      color: #666
    }

    .source {
      margin-top: 8px;
      font-size: 13px;
      color: #222;
      background: #f8f9ff;
      padding: 8px;
      border-radius: 6px
    }

    .controls {
      display: flex;
      gap: 8px;
      flex-wrap: wrap;
      margin-top: 12px
    }

    .btn {
      padding: 8px 12px;
      border-radius: 8px;
      border: 0;
      cursor: pointer
    }

    .btn.primary {
      background: #2b6ef6;
      color: #fff
    }

    .btn.warn {
      background: #ff7043;
      color: #fff
    }

    .btn.ghost {
      background: #fff;
      border: 1px solid #ddd
    }

    .locked {
      opacity: .6;
      pointer-events: none
    }
  </style>
</head>

<body>
  <div class="card" role="main">
    <h1>ISL202 — Lecture 16 MCQs (pages 72–78)</h1>
    <div class="meta">One question at a time. Select an option to get immediate feedback. Progress stored in
      <code>localStorage</code> under key <strong>Combined Handouts ISL202.pdf_lec16</strong>.</div>
    <div id="quiz"></div>
    <div class="controls">
      <button id="prevBtn" class="btn ghost">Previous</button>
      <button id="nextBtn" class="btn ghost">Next</button>
      <button id="submitBtn" class="btn primary">Submit (lock answers)</button>
      <button id="resetBtn" class="btn warn">Reset stats & answers</button>
      <div style="flex:1"></div>
      <div class="small">File source: <strong>Combined Handouts ISL202.pdf</strong> — Lecture pages
        <strong>72–78</strong></div>
    </div>
    <div class="stats" id="stats">
      <div>Total attempted: <span id="attempted">0</span></div>
      <div>Correct: <span id="correct">0</span> — Incorrect: <span id="incorrect">0</span></div>
      <div>Accuracy: <span id="accuracy">0</span>%</div>
      <div class="small">Reloading the page preserves progress for this lecture.</div>
    </div>
    <div class="source small" id="sourceDisplay"></div>
  </div>

  <script>

    document.addEventListener('DOMContentLoaded', function () {
      /* Embedded MCQs JSON (30 exam-style questions). Each question has: id, lecture, module, text, options, answerIndex, sourcePages */
      const QUESTIONS = [
        { "id": "lec16-q01", "lecture": "Lecture 16", "module": "Reconstruction of Ka'ba", "text": "Which event prompted the Quraysh to rebuild the Ka'ba during the Prophet’s lifetime?", "options": ["An earthquake that damaged the Ka'ba", "A fire inside the Ka'ba", "A flood that ruined its foundation", "A tribal dispute over the Black Stone"], "answerIndex": 1, "sourcePages": "72" },
        { "id": "lec16-q02", "lecture": "Lecture 16", "module": "Reconstruction of Ka'ba", "text": "How old was the Holy Prophet (PBUH) when the Quraysh decided to rebuild the Ka'ba?", "options": ["25 years old", "30 years old", "35 years old", "40 years old"], "answerIndex": 2, "sourcePages": "72" },
        { "id": "lec16-q03", "lecture": "Lecture 16", "module": "Reconstruction of Ka'ba", "text": "What method did the Prophet (PBUH) use to resolve the dispute among clan chiefs over who would replace the Black Stone?", "options": ["He asked each chief to fight for it", "He consulted a written law of precedence", "He spread his cloak and had chiefs lift its corners with the stone on it", "He asked the Ka'ba priests to decide"], "answerIndex": 2, "sourcePages": "72" },
        { "id": "lec16-q04", "lecture": "Lecture 16", "module": "Reconstruction of Ka'ba", "text": "Which of the following best describes the Prophet's action when resolving the Ka'ba dispute?", "options": ["He favored his own tribe openly", "He imposed a decision without consultation", "He shared the honour among chiefs to avoid bloodshed", "He postponed the decision indefinitely"], "answerIndex": 2, "sourcePages": "72" },
        { "id": "lec16-q05", "lecture": "Lecture 16", "module": "Emergence of Prophethood", "text": "What does the term 'By’that (بعثت)' primarily mean in the context of the Sirah?", "options": ["To migrate to another city", "To announce prophethood", "To perform pilgrimage", "To compile revelations"], "answerIndex": 1, "sourcePages": "73" },
        { "id": "lec16-q06", "lecture": "Lecture 16", "module": "Emergence of Prophethood", "text": "Which practice describes the Prophet’s routine before the first revelation?", "options": ["Frequent journeys to Syria", "Seclusion and worship in the Cave of Hira", "Leading public debates in the marketplace", "Studying previous scriptures publicly"], "answerIndex": 1, "sourcePages": "73" },
        { "id": "lec16-q07", "lecture": "Lecture 16", "module": "Emergence of Prophethood", "text": "At what age did the first revelation occur to the Prophet Muhammad (PBUH)?", "options": ["25 years", "30 years", "35 years", "40 years"], "answerIndex": 3, "sourcePages": "73" },
        { "id": "lec16-q08", "lecture": "Lecture 16", "module": "Emergence of Prophethood", "text": "On which night (day and month) did the first revelation take place according to the text?", "options": ["10th of Muharram (Monday)", "17th of Ramadan (Monday)", "1st of Shawwal (Friday)", "15th of Sha'ban (Saturday)"], "answerIndex": 1, "sourcePages": "73" },
        { "id": "lec16-q09", "lecture": "Lecture 16", "module": "Emergence of Prophethood", "text": "Who pressed the Prophet (PBUH) to ‘Read’ during the first revelation and how many times did this occur?", "options": ["Angel Jibril, three times", "Hazrat Khadija, once", "A heavenly voice, two times", "An elder of Quraysh, four times"], "answerIndex": 0, "sourcePages": "73" },
        { "id": "lec16-q10", "lecture": "Lecture 16", "module": "Emergence of Prophethood", "text": "Which Surah and verses were part of the first revelation recited by the Prophet (PBUH)?", "options": ["Surah Al-Fatiha verses 1–7", "Surah Al-Baqarah opening verses", "Surah Al-Alaq initial verses", "Surah Yaseen central verses"], "answerIndex": 2, "sourcePages": "73" },
        { "id": "lec16-q11", "lecture": "Lecture 16", "module": "Emergence of Prophethood", "text": "After the first revelation, what was the immediate physical reaction of the Prophet (PBUH) upon returning home?", "options": ["He immediately went out to preach", "He covered himself with a blanket and trembled", "He wrote down the verses at once", "He announced the news publicly in the market"], "answerIndex": 1, "sourcePages": "73" },
        { "id": "lec16-q12", "lecture": "Lecture 16", "module": "Emergence of Prophethood", "text": "Who encouraged the Prophet (PBUH) after the first revelation and reassured him by citing his good qualities?", "options": ["Hazrat Abu Bakr", "Hazrat Ali", "Hazrat Khadija", "Hazrat Abu Talib"], "answerIndex": 2, "sourcePages": "73" },
        { "id": "lec16-q13", "lecture": "Lecture 16", "module": "Emergence of Prophethood", "text": "Which characteristics of the Prophet did Hazrat Khadija mention to reassure him?", "options": ["His wealth and political power", "His mercy, care for orphans, and hospitality", "His military leadership", "His eloquence in speech"], "answerIndex": 1, "sourcePages": "73" },
        { "id": "lec16-q14", "lecture": "Lecture 16", "module": "Reconstruction of Ka'ba", "text": "Who paid the dowry of the Prophet (PBUH) when he married Hazrat Khadija as mentioned in the neighbouring events?", "options": ["Abu Talib paid 20 camels as dower", "Hazrat Khadija paid her own dower", "The Quraysh council provided the dower", "No dower was exchanged"], "answerIndex": 0, "sourcePages": "72" },
        { "id": "lec16-q15", "lecture": "Lecture 16", "module": "Reconstruction of Ka'ba", "text": "Which prior prophets were said to have rebuilt the Ka'ba before Prophet Muhammad's time according to the text?", "options": ["Only Hazrat Adam", "Hazrat Adam and Hazrat Abraham (and others)", "Hazrat Noah alone", "No prior prophets rebuilt it"], "answerIndex": 1, "sourcePages": "72" },
        { "id": "lec16-q16", "lecture": "Lecture 16", "module": "Emergence of Prophethood", "text": "Which companion is recorded as one who first supported the Prophet’s mission immediately after the first revelations?", "options": ["Hazrat Umar (RA)", "Hazrat Abu Bakr (RA)", "Hazrat Ali (RA)", "Hazrat Uthman (RA)"], "answerIndex": 1, "sourcePages": "73" },
        { "id": "lec16-q17", "lecture": "Lecture 16", "module": "Reconstruction of Ka'ba", "text": "During the Ka'ba reconstruction incident, what item did the Prophet (PBUH) place on the cloak before inviting chiefs to lift it?", "options": ["The Black Stone", "A copy of the Qur'an", "A pot of incense", "A white flag"], "answerIndex": 0, "sourcePages": "72" },
        { "id": "lec16-q18", "lecture": "Lecture 16", "module": "Emergence of Prophethood", "text": "How long did the Prophet (PBUH) reportedly spend in seclusion in the Cave of Hira prior to revelation?", "options": ["About three days total", "About three months", "About three years", "About three weeks"], "answerIndex": 2, "sourcePages": "73" },
        { "id": "lec16-q19", "lecture": "Lecture 16", "module": "Emergence of Prophethood", "text": "Which phrase best captures the Prophet’s state when Angel Jibril first commanded 'Read' and he could not initially comply?", "options": ["He was illiterate by choice", "He had genuine inability and fear in that moment", "He refused to obey", "He was already a teacher of reading"], "answerIndex": 1, "sourcePages": "73" },
        { "id": "lec16-q20", "lecture": "Lecture 16", "module": "Emergence of Prophethood", "text": "Which of these lines is from the verses revealed during the first revelation (Surah Al-Alaq) as cited in the text?", "options": ["'Read in the name of your Lord who created'", "'Praise be to Allah, Lord of the worlds'", "'Guide us on the straight path'", "'We have sent you as a mercy to mankind'"], "answerIndex": 0, "sourcePages": "73" },
        { "id": "lec16-q21", "lecture": "Lecture 16", "module": "Reconstruction of Ka'ba", "text": "Why did the Quraysh rebuild the Ka'ba at that specific time (according to the handout)?", "options": ["Because of internal tribal competition over the holy site", "As a response to damage caused by a careless woman starting a fire", "To expand its size for more pilgrims", "To convert it into a marketplace"], "answerIndex": 1, "sourcePages": "72" },
        { "id": "lec16-q22", "lecture": "Lecture 16", "module": "Emergence of Prophethood", "text": "Which person accompanied the Prophet (PBUH) on the trade journey to Syria where his honesty was observed?", "options": ["Abu Talib", "Maysara (Mesra), Hazrat Khadija's slave", "Hazrat Ali", "Hazrat Hamza"], "answerIndex": 1, "sourcePages": "73" },
        { "id": "lec16-q23", "lecture": "Lecture 16", "module": "Emergence of Prophethood", "text": "What reward/promise did Hazrat Khadija give the Prophet after his first revelation?", "options": ["She promised to divorce him if he failed", "She reassured him Allah will not disgrace him and praised his virtues", "She advised him to leave Makkah", "She told him to hide the revelation"], "answerIndex": 1, "sourcePages": "73" },
        { "id": "lec16-q24", "lecture": "Lecture 16", "module": "Reconstruction of Ka'ba", "text": "How did the chiefs respond to the Prophet's cloak solution for placing the Black Stone?", "options": ["They rejected it and fought", "They accepted and lifted the cloak together", "They demanded monetary compensation", "They appealed to a judge"], "answerIndex": 1, "sourcePages": "72" },
        { "id": "lec16-q25", "lecture": "Lecture 16", "module": "Emergence of Prophethood", "text": "Which of the following was NOT part of the immediate content of the first revelation quoted in the handout?", "options": ["Creation of man from a clinging substance", "Reference to the pen and being taught by it", "A command to establish prayer and charity", "The declaration that the Lord is Most Generous"], "answerIndex": 2, "sourcePages": "73" },
        { "id": "lec16-q26", "lecture": "Lecture 16", "module": "Reconstruction of Ka'ba", "text": "Who preserved the 'Master Copy' (Mushaf) compiled under Hazrat Abu Bakr before it was with Hazrat Uthman?", "options": ["Hazrat Umar and later Hafsa (daughter of Umar)", "Hazrat Ali kept it throughout", "It was lost and reconstructed later", "It was kept by the Prophet's family permanently"], "answerIndex": 0, "sourcePages": "72" },
        { "id": "lec16-q27", "lecture": "Lecture 16", "module": "Emergence of Prophethood", "text": "How did the Prophet (PBUH) describe his inability when Jibril told him to 'Read' the first two times?", "options": ["He spoke confidently and read immediately", "He replied 'I cannot read'", "He recited a prayer", "He fainted and could not respond"], "answerIndex": 1, "sourcePages": "73" },
        { "id": "lec16-q28", "lecture": "Lecture 16", "module": "Reconstruction of Ka'ba", "text": "Which role did Hazrat Umar (RA) play in the compilation of the Mushaf according to related pages around this lecture?", "options": ["He opposed any compilation", "He advised Hazrat Abu Bakr to collect the Quran into one copy", "He single-handedly compiled the Mushaf", "He destroyed variant copies"], "answerIndex": 1, "sourcePages": "72" },
        { "id": "lec16-q29", "lecture": "Lecture 16", "module": "Emergence of Prophethood", "text": "Which immediate comfort did Hazrat Khadija give the Prophet after the first revelation?", "options": ["She took him to the market to announce the news", "She wrapped him in a cloak and told him to rest", "She covered him with a blanket and spoke words of reassurance", "She sent him away to desert solitude"], "answerIndex": 2, "sourcePages": "73" },
        { "id": "lec16-q30", "lecture": "Lecture 16", "module": "Emergence of Prophethood", "text": "What is emphasized about the nature of the first revelation episodes in the handout?", "options": ["They were public and loudly announced", "They happened gradually and came with different circumstances", "They were compiled immediately into a book", "They were written down by the Prophet on stone"], "answerIndex": 1, "sourcePages": "73" }
      ];
      /* End QUESTIONS */

      const PDFNAME = 'Combined Handouts ISL202.pdf';
      const LECT_ID = 'lec16';
      const STORAGE_KEY = PDFNAME + '_' + LECT_ID;

      let state = {
        current: 0,
        answers: {}, // id -> selected index
        locked: false
      };

      // restore from localStorage
      const saved = localStorage.getItem(STORAGE_KEY);
      if (saved) {
        try { const s = JSON.parse(saved); state = Object.assign(state, s); } catch (e) { console.warn(e); }
      }


      function saveState() {
        localStorage.setItem(STORAGE_KEY, JSON.stringify(state));
      }

      function updateStats() {
        let attempted = 0, correct = 0, incorrect = 0;
        for (const q of QUESTIONS) {
          const sel = state.answers[q.id];
          if (typeof sel === 'number') { attempted++; if (sel === q.answerIndex) correct++; else incorrect++; }
        }
        document.getElementById('attempted').textContent = attempted;
        document.getElementById('correct').textContent = correct;
        document.getElementById('incorrect').textContent = incorrect;
        document.getElementById('accuracy').textContent = attempted ? Math.round((correct / attempted) * 100) : 0;
      }

      function render() {
        const q = QUESTIONS[state.current];
        const container = document.getElementById('quiz');
        container.innerHTML = `
      <div><span class="qcount">Question ${state.current + 1} / ${QUESTIONS.length}</span></div>
      <div class="stem">${q.text}</div>
      <ul class="options" id="opts"></ul>
      <div id="feedback" style="margin-top:8px"></div>
    `;
        const opts = document.getElementById('opts');
        q.options.forEach((text, i) => {
          const li = document.createElement('li');
          const btn = document.createElement('button');
          btn.className = 'opt';
          btn.textContent = String.fromCharCode(65 + i) + '. ' + text;
          btn.onclick = () => selectOption(i);
          // style if already answered
          const sel = state.answers[q.id];
          if (typeof sel === 'number') {
            if (i === sel) {
              if (state.locked) btn.classList.add(i === q.answerIndex ? 'correct' : 'incorrect');
              else btn.classList.add(i === q.answerIndex ? 'correct' : 'incorrect');
            }
          }
          li.appendChild(btn);
          opts.appendChild(li);
        });
        document.getElementById('sourceDisplay').textContent = 'Source page(s): ' + q.sourcePages;
        // lock UI if submitted
        document.getElementById('submitBtn').classList.toggle('locked', state.locked);
        document.getElementById('prevBtn').disabled = state.current === 0;
        document.getElementById('nextBtn').disabled = state.current === QUESTIONS.length - 1;
      // Dynamic heading update
        const currentLecture = QUESTIONS[state.current].lecture;
        const currentPages = QUESTIONS[state.current].sourcePages;
        document.querySelector("h1").textContent = `ISL202 — ${currentLecture} MCQs (pages ${currentPages})`;
}

      function selectOption(idx) {
        if (state.locked) { alert('Answers are locked for this session. Reset to reattempt.'); return; }
        const q = QUESTIONS[state.current];
        const prev = state.answers[q.id];
        // update answer
        state.answers[q.id] = idx;
        // immediate feedback
        const opts = document.querySelectorAll('.opt');
        opts.forEach((b, i) => {
          b.classList.remove('correct', 'incorrect');
          if (i === idx) {
            if (idx === q.answerIndex) b.classList.add('correct');
            else b.classList.add('incorrect');
          }
        });
        // update stats: if previously incorrect and now correct, adjust counts via state.answers
        updateStats();
        saveState();
      }

      document.getElementById('prevBtn').addEventListener('click', () => { state.current = Math.max(0, state.current - 1); render(); });
      document.getElementById('nextBtn').addEventListener('click', () => { state.current = Math.min(QUESTIONS.length - 1, state.current + 1); render(); });
      document.getElementById('resetBtn').addEventListener('click', () => {
        if (!confirm('Reset this lecture\'s stats and saved answers?')) return;
        state.answers = {}; state.current = 0; state.locked = false; saveState(); render();
      });
      document.getElementById('submitBtn').addEventListener('click', () => {
        if (!confirm('Submit and lock answers for this session? You can still Reset afterwards.')) return;
        state.locked = true; saveState(); render();
      });

      // initial render & stats
      updateStats();
      render();
    });

  </script>
</body>

</html>